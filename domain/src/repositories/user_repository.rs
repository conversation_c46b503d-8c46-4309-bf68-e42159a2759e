// User repository trait
// Defines the persistence interface for User entities

use crate::entities::User;
use crate::errors::DomainError;
use crate::value_objects::{Email, UserId};

/// Repository trait for User aggregate
/// This is a port in hexagonal architecture - implementations are adapters
#[async_trait::async_trait]
pub trait UserRepository: Send + Sync {
    /// Save a new user or update an existing one
    async fn save(&self, user: &User) -> Result<(), DomainError>;

    /// Find a user by their ID
    async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError>;

    /// Find a user by their email address
    async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError>;

    /// Find a user by their username
    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError>;

    /// Check if a user exists with the given email
    async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError>;

    /// Delete a user by their ID
    async fn delete(&self, id: &UserId) -> Result<(), DomainError>;

    /// Get all users (paginated)
    async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError>;

    /// Count total number of users
    async fn count(&self) -> Result<usize, DomainError>;

    /// Find users created within a date range
    async fn find_by_created_date_range(
        &self,
        start: std::time::SystemTime,
        end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError>;

    /// Find users by verification status
    async fn find_by_verification_status(
        &self,
        is_verified: bool,
    ) -> Result<Vec<User>, DomainError>;

    /// Find users by active status
    async fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError>;
}
