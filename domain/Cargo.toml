[package]
name = "auth-domain"
version = "0.1.0"
edition.workspace = true

# SECURITY-CRITICAL DEPENDENCIES - Exception to zero-dependency rule
# These cryptographic libraries are MANDATORY for security and have been audited
# as per CRYPTOGRAPHIC-POLICY.md and CRYPTOGRAPHY-REMEDIATION-GUIDE.md
[dependencies]
# Password hashing - OWASP 2025 compliant Argon2id (RFC 9106)
argon2 = { version = "0.6.0-rc.0", features = ["std", "password-hash"] }
password-hash = "0.6.0-rc.0"

# Cryptographically secure random number generation
rand = { version = "0.8.5", features = ["std", "std_rng"] }
rand_chacha = "0.3.1"

# Constant-time operations for timing attack prevention
subtle = "2.6.1"

# Base64 encoding for tokens
base64 = "0.22.1"

# Additional security utilities
zeroize = { version = "1.8.1", features = ["derive"] }

# Async trait support for repository interfaces
async-trait = "0.1"