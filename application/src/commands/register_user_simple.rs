// Simple user registration command implementation for TDD
// This is a minimal version to validate the core functionality

use crate::commands::base::{AsyncCommandHandler, Command};
use crate::errors::{ApplicationError, ApplicationResult};
use auth_domain::{
    entities::User,
    repositories::UserRepository,
    value_objects::{Email, Password, UserId},
};

/// Simple command to register a new user
#[derive(Debug, Clone)]
pub struct RegisterUserCommand {
    pub email: String,
    pub password: String,
}

impl Command for RegisterUserCommand {
    type Result = UserId;
}

/// Simple handler for user registration (no event publishing for now)
pub struct RegisterUserHandler<U>
where
    U: UserRepository,
{
    user_repository: U,
}

impl<U> RegisterUserHandler<U>
where
    U: UserRepository,
{
    pub fn new(user_repository: U) -> Self {
        Self { user_repository }
    }
}

#[async_trait::async_trait]
impl<U> AsyncCommandHandler<RegisterUserCommand> for RegisterUserHandler<U>
where
    U: UserRepository,
{
    async fn handle(&self, command: RegisterUserCommand) -> ApplicationResult<UserId> {
        // Parse and validate input
        let email = Email::new(&command.email)
            .map_err(|e| ApplicationError::InvalidInput(format!("Invalid email: {e}")))?;

        let password = Password::new(&command.password)
            .map_err(|e| ApplicationError::InvalidInput(format!("Invalid password: {e}")))?;

        // Check email uniqueness
        if self.user_repository.find_by_email(&email).await?.is_some() {
            return Err(ApplicationError::EmailAlreadyExists);
        }

        // Create new user
        let user = User::new(email.clone(), password)?;
        let user_id = user.id().clone();

        // Save user
        self.user_repository.save(&user).await?;

        Ok(user_id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::{
        entities::User,
        errors::DomainError,
        value_objects::{Email, Password},
    };
    use mockall::{mock, predicate::*};
    use std::time::Duration;
    use tokio::time::timeout;

    const TEST_TIMEOUT: Duration = Duration::from_secs(2);

    // Simple mock for UserRepository
    mock! {
        UserRepo {}

        #[async_trait::async_trait]
        impl UserRepository for UserRepo {
            async fn save(&self, user: &User) -> Result<(), DomainError>;
            async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError>;
            async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError>;
            async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError>;
            async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError>;
            async fn delete(&self, id: &UserId) -> Result<(), DomainError>;
            async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError>;
            async fn count(&self) -> Result<usize, DomainError>;
            async fn find_by_created_date_range(
                &self,
                start: std::time::SystemTime,
                end: std::time::SystemTime,
            ) -> Result<Vec<User>, DomainError>;
            async fn find_by_verification_status(&self, is_verified: bool) -> Result<Vec<User>, DomainError>;
            async fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError>;
        }
    }

    #[tokio::test]
    async fn should_register_new_user_successfully() {
        // Arrange
        let mut user_repo = MockUserRepo::new();

        user_repo
            .expect_find_by_email()
            .with(eq(Email::new("<EMAIL>").unwrap()))
            .times(1)
            .returning(|_| Ok(None));

        user_repo.expect_save().times(1).returning(|_| Ok(()));

        let handler = RegisterUserHandler::new(user_repo);
        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = timeout(TEST_TIMEOUT, handler.handle(command))
            .await
            .expect("Test timed out");

        // Assert
        assert!(result.is_ok());
        let user_id = result.unwrap();
        assert!(!user_id.to_string().is_empty());
    }

    #[tokio::test]
    async fn should_fail_when_email_already_exists() {
        // Arrange
        let mut user_repo = MockUserRepo::new();

        // Mock existing user
        let existing_user = User::new(
            Email::new("<EMAIL>").unwrap(),
            Password::new("SecurePass123!").unwrap(),
        )
        .unwrap();

        user_repo
            .expect_find_by_email()
            .with(eq(Email::new("<EMAIL>").unwrap()))
            .times(1)
            .returning(move |_| Ok(Some(existing_user.clone())));

        let handler = RegisterUserHandler::new(user_repo);
        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = timeout(TEST_TIMEOUT, handler.handle(command))
            .await
            .expect("Test timed out");

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::EmailAlreadyExists => (),
            other => panic!("Expected EmailAlreadyExists, got {other:?}"),
        }
    }

    #[tokio::test]
    async fn should_fail_with_invalid_email() {
        // Arrange
        let user_repo = MockUserRepo::new();

        let handler = RegisterUserHandler::new(user_repo);
        let command = RegisterUserCommand {
            email: "invalid-email".to_string(),
            password: "SecurePass123!".to_string(),
        };

        // Act
        let result = timeout(TEST_TIMEOUT, handler.handle(command))
            .await
            .expect("Test timed out");

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::InvalidInput(_) => (),
            other => panic!("Expected InvalidInput, got {other:?}"),
        }
    }

    #[tokio::test]
    async fn should_fail_with_weak_password() {
        // Arrange
        let user_repo = MockUserRepo::new();

        let handler = RegisterUserHandler::new(user_repo);
        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "weak".to_string(),
        };

        // Act
        let result = timeout(TEST_TIMEOUT, handler.handle(command))
            .await
            .expect("Test timed out");

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::InvalidInput(_) => (),
            other => panic!("Expected InvalidInput, got {other:?}"),
        }
    }

    #[tokio::test]
    async fn should_fail_with_empty_fields() {
        // Arrange
        let user_repo = MockUserRepo::new();

        let handler = RegisterUserHandler::new(user_repo);
        let command = RegisterUserCommand {
            email: "".to_string(),
            password: "".to_string(),
        };

        // Act
        let result = timeout(TEST_TIMEOUT, handler.handle(command))
            .await
            .expect("Test timed out");

        // Assert
        assert!(result.is_err());
        match result.unwrap_err() {
            ApplicationError::InvalidInput(_) => (),
            other => panic!("Expected InvalidInput, got {other:?}"),
        }
    }
}
