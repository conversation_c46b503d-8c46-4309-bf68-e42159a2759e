// Security module for application layer
// Provides rate limiting, timing attack protection, and secure error handling

pub mod constant_time_auth;
pub mod error_sanitizer;
pub mod rate_limiter;

// #[cfg(test)]
// mod tests; // Temporarily disabled due to testing infrastructure issues

pub use constant_time_auth::ConstantTimeAuthService;
pub use error_sanitizer::{ErrorSanitizer, SanitizedError};
pub use rate_limiter::{RateLimitKey, RateLimiter, RateLimiterConfig};
