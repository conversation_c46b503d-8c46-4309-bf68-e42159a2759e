// Mock implementations for repository traits using mockall
// Provides test doubles for all domain repositories

use auth_domain::{
    entities::{Role, Session, User},
    errors::DomainError,
    repositories::{RoleRepository, SessionRepository, UserRepository},
    value_objects::{Email, RoleId, SessionId, UserId},
};
use mockall::mock;

// Type alias for easier access in tests

// Mock UserRepository for testing
mock! {
    pub UserRepo {}

    #[async_trait::async_trait]
    impl UserRepository for UserRepo {
        async fn save(&self, user: &User) -> Result<(), DomainError>;
        async fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError>;
        async fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError>;
        async fn find_by_username(&self, username: &str) -> Result<Option<User>, DomainError>;
        async fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError>;
        async fn delete(&self, id: &UserId) -> Result<(), DomainError>;
        async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError>;
        async fn count(&self) -> Result<usize, DomainError>;
        async fn find_by_created_date_range(
            &self,
            start: std::time::SystemTime,
            end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError>;
        async fn find_by_verification_status(&self, is_verified: bool) -> Result<Vec<User>, DomainError>;
        async fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError>;
    }
}

// Mock SessionRepository for testing
mock! {
    pub SessionRepo {}

    #[async_trait::async_trait]
    impl SessionRepository for SessionRepo {
        async fn save(&self, session: &Session) -> Result<(), DomainError>;
        async fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, DomainError>;
        async fn find_active_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError>;
        async fn find_all_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError>;
        async fn delete(&self, id: &SessionId) -> Result<(), DomainError>;
        async fn delete_all_by_user(&self, user_id: &UserId) -> Result<(), DomainError>;
        async fn cleanup_expired_sessions(&self) -> Result<usize, DomainError>;
        async fn count_active_by_user(&self, user_id: &UserId) -> Result<usize, DomainError>;
        async fn find_by_ip_address(&self, ip_address: &str) -> Result<Vec<Session>, DomainError>;
        async fn find_by_created_date_range(
            &self,
            start: std::time::SystemTime,
            end: std::time::SystemTime,
        ) -> Result<Vec<Session>, DomainError>;
        async fn update_last_accessed(&self, id: &SessionId) -> Result<(), DomainError>;
        async fn invalidate(&self, id: &SessionId) -> Result<(), DomainError>;
        async fn invalidate_all_except(
            &self,
            user_id: &UserId,
            except_session_id: &SessionId,
        ) -> Result<(), DomainError>;
    }
}

// Mock RoleRepository for testing
mock! {
    pub RoleRepo {}

    #[async_trait::async_trait]
    impl RoleRepository for RoleRepo {
        async fn save(&self, role: &Role) -> Result<(), DomainError>;
        async fn find_by_id(&self, id: &RoleId) -> Result<Option<Role>, DomainError>;
        async fn find_by_name(&self, name: &str) -> Result<Option<Role>, DomainError>;
        async fn exists_by_name(&self, name: &str) -> Result<bool, DomainError>;
        async fn delete(&self, id: &RoleId) -> Result<(), DomainError>;
        async fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<Role>, DomainError>;
        async fn count(&self) -> Result<usize, DomainError>;
        async fn find_active(&self) -> Result<Vec<Role>, DomainError>;
        async fn find_by_user(&self, user_id: &UserId) -> Result<Vec<Role>, DomainError>;
        async fn find_by_permission(&self, permission: &str) -> Result<Vec<Role>, DomainError>;
        async fn find_children(&self, parent_id: &RoleId) -> Result<Vec<Role>, DomainError>;
        async fn get_hierarchy(&self) -> Result<Vec<Role>, DomainError>;
    }
}

// Mock Event Bus for testing domain events
mock! {
    pub EventBus {}

    #[async_trait::async_trait]
    impl crate::commands::base::EventPublisher for EventBus {
        async fn publish(&self, event: auth_domain::events::DomainEvent) -> Result<(), crate::errors::ApplicationError>;
        async fn publish_batch(&self, events: Vec<auth_domain::events::DomainEvent>) -> Result<(), crate::errors::ApplicationError>;
    }
}

// Mock Password Service for authentication testing
mock! {
    pub PasswordService {}

    #[async_trait::async_trait]
    impl PasswordHasher for PasswordService {
        async fn hash(&self, password: &str) -> Result<String, DomainError>;
        async fn verify(&self, hash: &str, password: &str) -> Result<bool, DomainError>;
    }
}

// Mock Token Service for authentication testing
mock! {
    pub TokenService {}

    impl TokenGenerator for TokenService {
        fn generate_access_token(&self, user_id: &UserId, session_id: &SessionId) -> Result<String, DomainError>;
        fn generate_refresh_token(&self) -> Result<String, DomainError>;
        fn validate_token(&self, token: &str) -> Result<TokenClaims, DomainError>;
    }
}

// These trait definitions would need to be added to the domain layer
// Placeholder traits for compilation
#[async_trait::async_trait]
pub trait EventBus: Send + Sync {
    async fn publish(&self, event: auth_domain::events::DomainEvent) -> Result<(), DomainError>;
    async fn publish_batch(
        &self,
        events: Vec<auth_domain::events::DomainEvent>,
    ) -> Result<(), DomainError>;
}

#[async_trait::async_trait]
pub trait PasswordHasher: Send + Sync {
    async fn hash(&self, password: &str) -> Result<String, DomainError>;
    async fn verify(&self, hash: &str, password: &str) -> Result<bool, DomainError>;
}

pub trait TokenGenerator: Send + Sync {
    fn generate_access_token(
        &self,
        user_id: &UserId,
        session_id: &SessionId,
    ) -> Result<String, DomainError>;
    fn generate_refresh_token(&self) -> Result<String, DomainError>;
    fn validate_token(&self, token: &str) -> Result<TokenClaims, DomainError>;
}

#[derive(Debug, Clone)]
pub struct TokenClaims {
    pub user_id: UserId,
    pub session_id: SessionId,
    pub expires_at: std::time::SystemTime,
}

// Helper struct for authentication responses
#[derive(Debug, Clone, PartialEq)]
pub struct AuthToken {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64, // seconds
}
