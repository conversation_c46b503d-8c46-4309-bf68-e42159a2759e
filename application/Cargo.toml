[package]
name = "auth-application"
version = "0.1.0"
edition.workspace = true

[dependencies]
auth-domain = { path = "../domain" }
tokio = { version = "1.47.0", features = ["rt", "rt-multi-thread", "macros", "sync"] }
thiserror = "1.0"
tracing = "0.1.41"
uuid = { version = "1.17.0", features = ["v4", "serde"] }
chrono = { version = "0.4.41", features = ["serde"] }
async-trait = "0.1"
argon2 = "0.6.0-rc.0"
subtle = "2.6.1"

[dev-dependencies]
mockall = "0.13.1"
tokio-test = "0.4.4"
proptest = "1.7.0"
tempfile = "3.20.0"
async-trait = "0.1"
futures = "0.3"